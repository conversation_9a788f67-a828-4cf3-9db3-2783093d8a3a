import { sqliteAdapter } from '@payloadcms/db-sqlite'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import { buildConfig, getPayload } from 'payload'
import sharp from 'sharp'
import { fileURLToPath } from 'url'

import { HeroSlides } from '../collections/HeroSlides'
import { Media } from '../collections/Media'
import { Members } from '../collections/Members'
import { News } from '../collections/News'
import { Pages } from '../collections/Pages'
import { ResearchAreas } from '../collections/ResearchAreas'
import { Users } from '../collections/Users'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

// Helper function removed - using plain text now

const seed = async (): Promise<void> => {
  const config = buildConfig({
    admin: {
      user: Users.slug,
      importMap: {
        baseDir: path.resolve(dirname, '..'),
      },
    },
    collections: [Users, Media, Members, ResearchAreas, News, Pages, HeroSlides],
    editor: lexicalEditor(),
    secret: process.env.PAYLOAD_SECRET || '',
    typescript: {
      outputFile: path.resolve(dirname, '../payload-types.ts'),
    },
    db: sqliteAdapter({
      client: {
        url: process.env.DATABASE_URI || '',
      },
    }),
    sharp,
    plugins: [],
  })

  const payload = await getPayload({ config })

  console.log('🌱 Seeding database...')

  try {
    // Clear existing data
    console.log('Clearing existing data...')
    await payload.delete({ collection: 'hero-slides', where: {} })
    await payload.delete({ collection: 'members', where: {} })
    await payload.delete({ collection: 'research-areas', where: {} })
    await payload.delete({ collection: 'news', where: {} })
    await payload.delete({ collection: 'pages', where: {} })

    // Seed Hero Slides
    console.log('Seeding hero slides...')
    const heroSlides = [
      {
        title: 'Welcome to the New Website of BCMI!',
        subtitle: 'Bravo! The New Website is Online! More beautiful and more convenient!',
        buttonText: 'Learn More',
        buttonUrl: '/research',
        order: 1,
        isActive: true,
      },
      {
        title: 'Novel Fatigue Detection System',
        subtitle: 'Combining EEG, EOG, Infrared Camera, Kinect and Gripping Power all together!',
        buttonText: 'View Research',
        buttonUrl: '/research',
        order: 2,
        isActive: true,
      },
      {
        title: 'New Pinyin Input Method',
        subtitle:
          'This time, we redefined the pinyin input method which gives you absolute new experience!',
        buttonText: 'Try Demo',
        buttonUrl: '/research',
        order: 3,
        isActive: true,
      },
      {
        title: 'Dialogue System',
        subtitle:
          'Dialogue System, but is absolutely different from all the existing systems! Probing the world of artificial intelligence!',
        buttonText: 'Explore',
        buttonUrl: '/research',
        order: 4,
        isActive: true,
      },
    ]

    for (const slide of heroSlides) {
      await payload.create({
        collection: 'hero-slides',
        data: slide,
      })
    }

    // Seed Members
    console.log('Seeding members...')
    const members = [
      {
        name: 'Prof. Bao-Liang Lu',
        category: 'faculty' as const,
        position: 'Professor, Director of BCMI',
        email: '<EMAIL>',
        personalWebsite: 'http://bcmi.sjtu.edu.cn/~blu',
        researchInterests:
          'Brain-Computer Interface, Machine Learning, Neural Networks, Cognitive Computing',
        bio: 'Prof. Bao-Liang Lu is a Professor and the Director of the Center for Brain-like Computing and Machine Intelligence at Shanghai Jiao Tong University. His research focuses on brain-computer interfaces, machine learning, and neural networks.',
        isActive: true,
      },
      {
        name: 'Prof. Liqing Zhang',
        category: 'faculty' as const,
        position: 'Professor, Co-Director of BCMI',
        email: '<EMAIL>',
        personalWebsite: 'http://bcmi.sjtu.edu.cn/~zhang',
        researchInterests:
          'Computer Vision, Pattern Recognition, Machine Learning, Image Processing',
        bio: 'Prof. Liqing Zhang is a Professor and Co-Director of BCMI. He specializes in computer vision, pattern recognition, and machine learning applications.',
        isActive: true,
      },
      {
        name: 'Dr. Wei-Long Zheng',
        category: 'phd' as const,
        position: 'Postdoctoral Researcher',
        mentor: 'Prof. Bao-Liang Lu',
        email: '<EMAIL>',
        researchInterests: 'EEG-based Emotion Recognition, Brain-Computer Interface, Deep Learning',
        isActive: true,
      },
      {
        name: 'Zhang Ming',
        category: 'phd' as const,
        position: 'PhD Student',
        mentor: 'Prof. Liqing Zhang',
        researchInterests: 'Computer Vision, Object Detection, Deep Learning',
        isActive: true,
      },
      {
        name: 'Li Wei',
        category: 'graduate' as const,
        position: 'Master Student',
        mentor: 'Prof. Bao-Liang Lu',
        researchInterests: 'Natural Language Processing, Machine Learning',
        isActive: true,
      },
      {
        name: 'Wang Xiaoli',
        category: 'graduate' as const,
        position: 'Master Student',
        mentor: 'Prof. Liqing Zhang',
        researchInterests: 'Image Processing, Pattern Recognition',
        isActive: true,
      },
    ]

    for (const member of members) {
      await payload.create({
        collection: 'members',
        data: member,
      })
    }

    // Seed Research Areas
    console.log('Seeding research areas...')
    const researchAreas = [
      {
        title: 'Brain Computer Interface',
        shortDescription:
          'Developing advanced brain-computer interfaces for medical and assistive applications using EEG, fMRI, and other neuroimaging techniques.',
        description:
          'Our Brain-Computer Interface research focuses on developing non-invasive methods to decode brain signals and translate them into control commands for external devices. We work on EEG-based emotion recognition, motor imagery classification, and cognitive state monitoring.',
        demos: [
          {
            title: 'EEG-based Emotion Recognition',
            description:
              'Real-time emotion recognition system using EEG signals with deep learning algorithms.',
            url: '/demos/emotion-recognition',
          },
          {
            title: 'Motor Imagery BCI',
            description:
              'Brain-computer interface for controlling external devices through motor imagery.',
            url: '/demos/motor-imagery',
          },
          {
            title: 'Fatigue Detection System',
            description:
              'Multi-modal fatigue detection combining EEG, EOG, and behavioral measures.',
            url: '/demos/fatigue-detection',
          },
        ],
        order: 1,
        isActive: true,
      },
      {
        title: 'Computer Vision',
        shortDescription:
          'Advanced computer vision algorithms for object recognition, scene understanding, and visual perception systems.',
        description:
          'Our computer vision research encompasses object detection, image classification, scene understanding, and visual perception. We develop novel deep learning architectures and algorithms for various vision tasks.',
        demos: [
          {
            title: 'Object Detection System',
            description:
              'Real-time object detection and tracking using state-of-the-art deep learning models.',
            url: '/demos/object-detection',
          },
          {
            title: 'Face Recognition',
            description: 'Advanced face recognition system with high accuracy and robustness.',
            url: '/demos/face-recognition',
          },
        ],
        order: 2,
        isActive: true,
      },
      {
        title: 'Natural Language Processing',
        shortDescription:
          'Intelligent text processing, language understanding, and conversational AI systems.',
        description:
          'Our NLP research covers various aspects of language processing including dialogue systems, machine translation, text analysis, and conversational AI. We focus on developing practical applications for Chinese language processing.',
        demos: [
          {
            title: 'Intelligent Dialogue System',
            description: 'Advanced conversational AI system with natural language understanding.',
            url: '/demos/dialogue-system',
          },
          {
            title: 'Pinyin Input Method',
            description:
              'AI-powered Chinese input method with improved accuracy and user experience.',
            url: '/demos/pinyin-input',
          },
        ],
        order: 3,
        isActive: true,
      },
      {
        title: 'Machine Learning',
        shortDescription:
          'Novel machine learning algorithms, deep learning architectures, and AI applications.',
        description:
          'Our machine learning research focuses on developing novel algorithms, deep learning architectures, and their applications in various domains including healthcare, robotics, and cognitive computing.',
        demos: [
          {
            title: 'Deep Learning Framework',
            description: 'Custom deep learning framework optimized for neural signal processing.',
            url: '/demos/deep-learning',
          },
          {
            title: 'Reinforcement Learning',
            description: 'Advanced RL algorithms for complex decision-making tasks.',
            url: '/demos/reinforcement-learning',
          },
        ],
        order: 4,
        isActive: true,
      },
    ]

    for (const area of researchAreas) {
      await payload.create({
        collection: 'research-areas',
        data: area,
      })
    }

    // Seed News and Events
    console.log('Seeding news and events...')
    const newsItems = [
      {
        title: 'BCMI Laboratory Wins Best Paper Award at IJCAI 2024',
        type: 'award' as const,
        excerpt:
          'Our research on brain-computer interfaces has been recognized with the best paper award at the International Joint Conference on Artificial Intelligence.',
        content:
          'We are proud to announce that our paper "Advanced EEG-based Emotion Recognition using Deep Learning" has won the Best Paper Award at IJCAI 2024. This recognition highlights our commitment to advancing the field of brain-computer interfaces.',
        publishedDate: '2024-01-15',
        isPublished: true,
        isFeatured: true,
      },
      {
        title: 'International Workshop on Brain-Computer Interfaces 2024',
        type: 'event' as const,
        excerpt:
          'Join us for a comprehensive workshop on the latest advances in BCI technology, featuring international experts and hands-on sessions.',
        content:
          'The BCMI Laboratory is organizing an international workshop on Brain-Computer Interfaces. The event will feature keynote speakers, technical sessions, and hands-on demonstrations.',
        publishedDate: '2024-01-10',
        eventDate: '2024-03-15',
        eventLocation: 'SJTU Campus, Shanghai, China',
        isPublished: true,
        isFeatured: true,
      },
      {
        title: 'New Publication in Nature Machine Intelligence',
        type: 'publication' as const,
        excerpt:
          'Our latest research on neural decoding algorithms has been published in Nature Machine Intelligence, a top-tier journal in AI research.',
        content:
          'We are excited to share our latest publication in Nature Machine Intelligence. The paper presents novel algorithms for decoding neural signals with improved accuracy and efficiency.',
        publishedDate: '2024-01-05',
        externalUrl: 'https://nature.com/articles/example',
        isPublished: true,
        isFeatured: false,
      },
      {
        title: 'BCMI Lab Welcomes New PhD Students',
        type: 'news' as const,
        excerpt:
          'We are pleased to welcome five new PhD students to our research team, bringing fresh perspectives and expertise to our ongoing projects.',
        content:
          'The BCMI Laboratory is growing! We welcome five talented new PhD students who will be working on various research projects including brain-computer interfaces, computer vision, and machine learning.',
        publishedDate: '2023-12-20',
        isPublished: true,
        isFeatured: false,
      },
      {
        title: 'Collaboration Agreement with MIT CSAIL',
        type: 'news' as const,
        excerpt:
          'BCMI Laboratory has signed a collaboration agreement with MIT CSAIL to advance research in artificial intelligence and brain-computer interfaces.',
        content:
          'We are excited to announce our new collaboration with MIT Computer Science and Artificial Intelligence Laboratory (CSAIL). This partnership will facilitate joint research projects and student exchanges.',
        publishedDate: '2023-12-15',
        isPublished: true,
        isFeatured: true,
      },
    ]

    for (const newsItem of newsItems) {
      await payload.create({
        collection: 'news',
        data: newsItem,
      })
    }

    // Seed Pages
    console.log('Seeding pages...')
    const pages = [
      {
        title: 'About BCMI Laboratory',
        slug: 'about',
        content:
          'The Center for Brain-like Computing and Machine Intelligence (BCMI) is a leading research center at Shanghai Jiao Tong University. Founded by Prof. Bao-Liang Lu and Prof. Liqing Zhang, our mission is to understand the mechanisms of intelligent information processing and cognitive processes in the brain, and to develop new computing structures and algorithms for information technology.',
        excerpt: 'Learn more about the BCMI Laboratory, our mission, and our research goals.',
        isPublished: true,
      },
      {
        title: 'Research Opportunities',
        slug: 'opportunities',
        content:
          'The BCMI Laboratory offers various research opportunities for undergraduate students, graduate students, and postdoctoral researchers. We welcome applications from motivated individuals interested in brain-computer interfaces, machine learning, computer vision, and natural language processing.',
        excerpt: 'Explore research opportunities and how to join the BCMI Laboratory.',
        isPublished: true,
      },
    ]

    for (const page of pages) {
      await payload.create({
        collection: 'pages',
        data: page,
      })
    }

    console.log('✅ Database seeded successfully!')
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

export default seed
